import jsPDF from 'jspdf'
import { getProducts } from './storage'

export const generatePDF = async (invoice, customer) => {
  const doc = new jsPDF()
  const products = getProducts()
  
  // Set font
  doc.setFont('helvetica')
  
  // Header - Company Info
  doc.setFontSize(20)
  doc.setTextColor(40, 40, 40)
  doc.text('ROTI RAGIL', 20, 30)

  doc.setFontSize(10)
  doc.setTextColor(100, 100, 100)
  doc.text('Telp: 0895402652626', 20, 38)
  
  // Invoice Title
  doc.setFontSize(16)
  doc.setTextColor(40, 40, 40)
  doc.text('INVOICE', 150, 30)
  
  // Invoice Info
  doc.setFontSize(10)
  doc.text(`No. Invoice: ${invoice.nomorInvoice}`, 150, 40)
  doc.text(`Tanggal: ${new Date(invoice.tanggal).toLocaleDateString('id-ID')}`, 150, 47)
  doc.text(`Status: ${getStatusText(invoice.status)}`, 150, 54)

  // Line separator
  doc.setLineWidth(0.5)
  doc.line(20, 58, 190, 58)
  
  // Customer Info - Simplified (no address/email/phone)
  doc.setFontSize(12)
  doc.setTextColor(40, 40, 40)
  doc.text('Kepada:', 20, 73)

  doc.setFontSize(10)
  doc.text(customer.nama, 20, 83)

  let yPos = 83

  // Table Header
  const tableStartY = yPos + 20
  doc.setFillColor(240, 240, 240)
  doc.rect(20, tableStartY, 170, 10, 'F')
  
  doc.setFontSize(9)
  doc.setTextColor(40, 40, 40)
  doc.text('No', 25, tableStartY + 7)
  doc.text('Produk', 40, tableStartY + 7)
  doc.text('Qty', 120, tableStartY + 7)
  doc.text('Harga', 140, tableStartY + 7)
  doc.text('Total', 170, tableStartY + 7)
  
  // Table Content
  let currentY = tableStartY + 15
  let grandTotal = 0
  
  invoice.items.forEach((item, index) => {
    const product = products.find(p => p.id === item.produkId)
    const productName = product ? product.nama : 'Produk Tidak Ditemukan'
    const itemTotal = item.quantity * item.harga
    grandTotal += itemTotal
    
    doc.text((index + 1).toString(), 25, currentY)
    doc.text(productName, 40, currentY)
    doc.text(item.quantity.toString(), 125, currentY)
    doc.text(formatCurrency(item.harga), 140, currentY)
    doc.text(formatCurrency(itemTotal), 165, currentY)
    
    currentY += 10
  })
  
  // Total Section
  const totalY = currentY + 10
  doc.setLineWidth(0.3)
  doc.line(140, totalY - 5, 190, totalY - 5)
  
  doc.setFontSize(11)
  doc.setFont('helvetica', 'bold')
  doc.text('TOTAL:', 140, totalY)
  doc.text(formatCurrency(grandTotal), 165, totalY)
  
  // Footer
  const footerY = totalY + 30
  doc.setFontSize(9)
  doc.setFont('helvetica', 'normal')
  doc.setTextColor(100, 100, 100)
  doc.text('Terima kasih atas kepercayaan Anda!', 20, footerY)
  doc.text('Pembayaran dapat dilakukan melalui transfer bank atau tunai.', 20, footerY + 7)
  
  // Save the PDF
  doc.save(`Invoice-${invoice.nomorInvoice}.pdf`)
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR'
  }).format(amount)
}

const getStatusText = (status) => {
  switch (status) {
    case 'paid':
      return 'Lunas'
    case 'sent':
      return 'Terkirim'
    default:
      return 'Draft'
  }
}
